import React, { useState, useEffect, useRef } from 'react';
import { aiInterviewEvaluationsApi } from '../services/api';

const AIInterviewEvaluationRunner = ({ selectedDatasetId, onRun, onPollingUpdate }) => {
  const [running, setRunning] = useState(false);
  const [lastResult, setLastResult] = useState(null);
  const [pollingEvaluations, setPollingEvaluations] = useState(new Set());

  const pollingIntervals = useRef(new Map());

  // Cleanup polling intervals on unmount
  useEffect(() => {
    return () => {
      pollingIntervals.current.forEach(intervalId => clearInterval(intervalId));
      pollingIntervals.current.clear();
    };
  }, []);

  const startPolling = (evaluationId) => {
    if (pollingIntervals.current.has(evaluationId)) {
      return; // Already polling this evaluation
    }

    setPollingEvaluations(prev => new Set([...prev, evaluationId]));

    const intervalId = setInterval(async () => {
      try {
        const response = await aiInterviewEvaluationsApi.getStatus(evaluationId);
        const evaluation = response.data;

        if (evaluation.status === 'completed' || evaluation.status === 'error') {
          // Stop polling
          clearInterval(intervalId);
          pollingIntervals.current.delete(evaluationId);
          setPollingEvaluations(prev => {
            const newSet = new Set(prev);
            newSet.delete(evaluationId);
            return newSet;
          });

          // Update the evaluation in the parent component
          if (onPollingUpdate) {
            onPollingUpdate(evaluation);
          }

          // Update last result if this is the most recent
          if (lastResult && lastResult.id === evaluationId) {
            setLastResult(evaluation);
          }
        }
      } catch (error) {
        console.error('Error polling evaluation status:', error);
        // Continue polling even if there's an error
      }
    }, 30000); // Poll every 30 seconds

    pollingIntervals.current.set(evaluationId, intervalId);
  };

  const handleRun = async () => {
    if (!selectedDatasetId) {
      alert('Please select a dataset first');
      return;
    }

    try {
      setRunning(true);
      const result = await onRun(selectedDatasetId);
      setLastResult(result);

      // Start polling if the result has "in_progress" status
      if (result && result.status === "in_progress") {
        startPolling(result.id);
      }
    } catch (error) {
      alert('Failed to run AI Interview evaluation');
    } finally {
      setRunning(false);
    }
  };

  const getStatusDisplay = (status) => {
    switch (status) {
      case 'in_progress':
        return { text: 'Processing...', color: '#ffc107', bgColor: '#fff3cd' };
      case 'completed':
        return { text: 'Completed', color: '#28a745', bgColor: '#d4edda' };
      case 'error':
        return { text: 'Error', color: '#dc3545', bgColor: '#f8d7da' };
      default:
        return { text: status, color: '#6c757d', bgColor: '#e9ecef' };
    }
  };

  const formatOutput = (output) => {
    if (!output) return 'No output available';
    
    if (typeof output === 'string') {
      return output;
    }

    if (typeof output === 'object') {
      // Try to format the insight summary if available
      if (output.insight) {
        return output.insight;
      }
      
      // Otherwise show a summary of evaluations
      if (output.evaluations) {
        const criteriaCount = Object.keys(output.evaluations).filter(key => 
          key !== 'list_analysis' && key !== 'summary'
        ).length;
        return `Evaluation completed for ${criteriaCount} criteria. Click "View Details" to see full results.`;
      }
      
      return JSON.stringify(output, null, 2);
    }

    return String(output);
  };

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '16px',
      backgroundColor: 'white'
    }}>
      <h3 style={{ marginTop: 0 }}>Run AI Interview Evaluation</h3>

      {!selectedDatasetId && (
        <div style={{
          padding: '12px',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '4px',
          color: '#856404',
          marginBottom: '16px'
        }}>
          Please select a dataset from the Dataset Management section above to run an evaluation.
        </div>
      )}

      <div style={{ marginBottom: '16px' }}>
        <p style={{ margin: '0 0 8px 0', color: '#666' }}>
          This evaluation will run a 3-step analysis:
        </p>
        <ol style={{ margin: '0 0 0 20px', color: '#666' }}>
          <li>Question Evaluator - Analyze each question-answer pair</li>
          <li>Summary Evaluator - Summarize all question evaluations</li>
          <li>Insight Summary - Generate final insights and recommendations</li>
        </ol>
      </div>

      <button
        onClick={handleRun}
        disabled={running || !selectedDatasetId}
        style={{
          padding: '10px 20px',
          border: 'none',
          borderRadius: '4px',
          backgroundColor: running || !selectedDatasetId ? '#6c757d' : '#28a745',
          color: 'white',
          cursor: running || !selectedDatasetId ? 'not-allowed' : 'pointer',
          fontSize: '16px',
          fontWeight: 'bold',
          marginBottom: '16px'
        }}
      >
        {running ? 'Starting Evaluation...' : 'Run AI Interview Evaluation'}
      </button>

      {/* Processing notification */}
      {running && (
        <div style={{
          marginBottom: '20px',
          padding: '15px',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '4px',
          color: '#856404'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#856404' }}>
            🔄 Starting AI Interview Evaluation
          </h4>
          <p style={{ margin: 0 }}>
            The evaluation is being initialized. This process will run in the background and may take several minutes to complete.
            The page will automatically update when results are available.
          </p>
        </div>
      )}

      {/* Active polling notifications */}
      {pollingEvaluations.size > 0 && (
        <div style={{
          marginBottom: '20px',
          padding: '15px',
          backgroundColor: '#e3f2fd',
          border: '1px solid #bbdefb',
          borderRadius: '4px',
          color: '#1565c0'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#1565c0' }}>
            ⏳ Evaluations in Progress
          </h4>
          <p style={{ margin: 0 }}>
            {pollingEvaluations.size} evaluation(s) are currently being processed. 
            Results will appear automatically when completed.
          </p>
        </div>
      )}
    </div>
  );
};

export default AIInterviewEvaluationRunner;
